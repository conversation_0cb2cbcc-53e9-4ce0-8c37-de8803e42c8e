import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { useAppStore, Message } from '../store';

// Import highlight.js styles
import 'highlight.js/styles/github-dark.css';

interface MessageComponentProps {
  message: Message;
}

const MessageComponent: React.FC<MessageComponentProps> = ({ message }) => {
  const getMessageStyles = () => {
    switch (message.type) {
      case 'user':
        return 'message-bubble message-bubble-user';
      case 'ai':
        return 'message-bubble message-bubble-ai';
      case 'tool':
        return 'message-bubble message-bubble-tool';
      default:
        return 'message-bubble';
    }
  };

  const getMessageLabel = () => {
    switch (message.type) {
      case 'user':
        return 'You';
      case 'ai':
        return 'AI Assistant';
      case 'tool':
        return 'Tool Output';
      default:
        return 'Message';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={getMessageStyles()}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            message.type === 'user' ? 'bg-accent' :
            message.type === 'ai' ? 'bg-success' : 'bg-warning'
          }`} />
          <span className="text-sm font-semibold text-primary">
            {getMessageLabel()}
          </span>
        </div>
        <span className="text-xs text-tertiary">
          {formatTimestamp(message.timestamp)}
        </span>
      </div>

      <div className="text-primary">
        {message.type === 'user' ? (
          // User messages: simple text with preserved whitespace
          <div className="whitespace-pre-wrap font-medium leading-relaxed">
            {message.content}
          </div>
        ) : (
          // AI and tool messages: full markdown rendering
          <div className="prose prose-invert prose-sm max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeHighlight]}
              components={{
                // Custom styling for markdown elements
                h1: ({ children }) => (
                  <h1 className="text-xl font-bold text-primary mb-4 mt-6 first:mt-0">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-lg font-semibold text-primary mb-3 mt-5 first:mt-0">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-base font-semibold text-primary mb-2 mt-4 first:mt-0">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-primary mb-3 last:mb-0 leading-relaxed">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside text-primary mb-4 space-y-1 pl-2">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside text-primary mb-4 space-y-1 pl-2">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-primary leading-relaxed">{children}</li>
                ),
                blockquote: ({ children }) => (
                  <div className="blockquote">
                    {children}
                  </div>
                ),
                code: ({ children, className, ...props }: any) => {
                  const isInline = !className?.includes('language-');
                  if (isInline) {
                    return (
                      <code className="inline-code">
                        {children}
                      </code>
                    );
                  }
                  return (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  },
                  pre: ({ children }) => (
                    <pre className="code-block">
                      {children}
                    </pre>
                  ),
                  table: ({ children }) => (
                    <div className="table-container">
                      <table>
                        {children}
                      </table>
                    </div>
                  ),
                  thead: ({ children }) => (
                    <thead>
                      {children}
                    </thead>
                  ),
                  th: ({ children }) => (
                    <th>
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td>
                      {children}
                    </td>
                  ),
                  a: ({ children, href }) => (
                    <a
                      href={href}
                      className="text-accent hover:text-accent transition-colors underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {children}
                    </a>
                  ),
                  strong: ({ children }) => (
                    <strong className="font-semibold text-primary">
                      {children}
                    </strong>
                  ),
                  em: ({ children }) => (
                    <em className="italic text-primary">
                      {children}
                    </em>
                  ),
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          )}
      </div>
    </div>
  );
};

export const MainInterface: React.FC = () => {
  const { messages } = useAppStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto custom-scrollbar px-4 py-4 pb-6"
      style={{ height: 'calc(100vh - 180px)' }}
    >
      <div className="w-full min-h-full">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center min-h-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-surface rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-md">
                <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
              </div>
              <div className="text-xl font-semibold text-primary mb-2">Ready to assist</div>
              <div className="text-secondary">Start a conversation by typing a message below</div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageComponent key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
    </div>
  );
};
