import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { useAppStore, Message } from '../store';

// Import highlight.js styles
import 'highlight.js/styles/github-dark.css';

interface MessageComponentProps {
  message: Message;
}

const MessageComponent: React.FC<MessageComponentProps> = ({ message }) => {
  const getMessageStyles = () => {
    switch (message.type) {
      case 'user':
        return 'bg-dark-accent/10 border-dark-accent/30 ml-8';
      case 'ai':
        return 'bg-dark-surface border-dark-border mr-8';
      case 'tool':
        return 'bg-yellow-900/20 border-yellow-600/30 mx-4';
      default:
        return 'bg-dark-surface border-dark-border';
    }
  };

  const getMessageLabel = () => {
    switch (message.type) {
      case 'user':
        return 'You';
      case 'ai':
        return 'AI Assistant';
      case 'tool':
        return 'Tool Output';
      default:
        return 'Message';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={`border rounded-lg p-4 mb-4 ${getMessageStyles()}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-dark-text-secondary">
          {getMessageLabel()}
        </span>
        <span className="text-xs text-dark-text-secondary">
          {formatTimestamp(message.timestamp)}
        </span>
      </div>
      
      <div className="text-dark-text">
        {message.type === 'user' ? (
          // User messages: simple text with preserved whitespace
          <div className="whitespace-pre-wrap font-medium">
            {message.content}
          </div>
        ) : (
          // AI and tool messages: full markdown rendering
          <div className="prose prose-invert prose-sm max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeHighlight]}
              components={{
                // Custom styling for markdown elements
                h1: ({ children }) => (
                  <h1 className="text-xl font-bold text-dark-text mb-4 mt-6 first:mt-0">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-lg font-semibold text-dark-text mb-3 mt-5 first:mt-0">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-base font-semibold text-dark-text mb-2 mt-4 first:mt-0">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-dark-text mb-3 last:mb-0 leading-relaxed">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside text-dark-text mb-3 space-y-1">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside text-dark-text mb-3 space-y-1">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-dark-text">{children}</li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-dark-accent pl-4 italic text-dark-text-secondary mb-3">
                    {children}
                  </blockquote>
                ),
                code: ({ inline, children, className }) => {
                  if (inline) {
                    return (
                      <code className="bg-dark-bg px-1.5 py-0.5 rounded text-sm font-mono text-dark-accent">
                        {children}
                      </code>
                    );
                  }
                  return (
                      <code className={className}>
                        {children}
                      </code>
                    );
                  },
                  pre: ({ children }) => (
                    <pre className="bg-dark-bg rounded-lg p-4 overflow-x-auto mb-3 border border-dark-border">
                      {children}
                    </pre>
                  ),
                  table: ({ children }) => (
                    <div className="overflow-x-auto mb-3">
                      <table className="min-w-full border border-dark-border rounded-lg">
                        {children}
                      </table>
                    </div>
                  ),
                  thead: ({ children }) => (
                    <thead className="bg-dark-surface">
                      {children}
                    </thead>
                  ),
                  th: ({ children }) => (
                    <th className="border border-dark-border px-3 py-2 text-left font-semibold text-dark-text">
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td className="border border-dark-border px-3 py-2 text-dark-text">
                      {children}
                    </td>
                  ),
                  a: ({ children, href }) => (
                    <a
                      href={href}
                      className="text-dark-accent hover:text-blue-400 underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {children}
                    </a>
                  ),
                  strong: ({ children }) => (
                    <strong className="font-semibold text-dark-text">
                      {children}
                    </strong>
                  ),
                  em: ({ children }) => (
                    <em className="italic text-dark-text">
                      {children}
                    </em>
                  ),
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          )}
      </div>
    </div>
  );
};

export const MainInterface: React.FC = () => {
  const { messages } = useAppStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  return (
    <div 
      ref={containerRef}
      className="flex-1 overflow-y-auto p-4 pb-32"
      style={{ height: 'calc(100vh - 140px)' }}
    >
      <div className="max-w-4xl mx-auto">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-dark-text-secondary">
            <div className="text-center">
              <div className="text-lg mb-2">Ready to assist</div>
              <div className="text-sm">Start a conversation by typing a message below</div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageComponent key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
    </div>
  );
};
