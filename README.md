# Arien Agent - Electron Desktop Application

A minimal Electron desktop application with React, TypeScript, and Zustand state management, featuring two main UI components for conversational AI interaction.

## Features

### 🎯 Core Components

#### 1. **PromptInput Component**
- **Position**: Fixed at the bottom of the application window
- **Features**:
  - Rich text input area with multiline support (textarea)
  - Command history navigation using up/down arrow keys
  - Auto-resize functionality for multiline content
  - Submit functionality (Enter key or submit button)
  - Shift+Enter for new lines
  - Clean visual styling with dark theme
  - Character count indicator for long messages (>500 chars)
  - Keyboard shortcuts display
- **State Management**: Uses Zustand to store and retrieve prompt history
- **Accessibility**: Proper focus management and keyboard navigation

#### 2. **MainInterface Component**
- **Position**: Main content area above the PromptInput component
- **Features**:
  - Scrollable conversation history display
  - Support for different message types:
    - User prompts (styled with blue accent)
    - AI responses with full markdown rendering
    - Tool outputs with yellow accent
  - **Markdown rendering capabilities**:
    - Headers (H1, H2, H3)
    - Lists (ordered and unordered)
    - Links with external opening
    - Emphasis (bold, italic)
    - Code blocks with syntax highlighting
    - Inline code snippets
    - Tables with proper formatting
    - Blockquotes
  - Auto-scroll to bottom when new messages are added
  - Responsive design that works with the fixed bottom input
  - Timestamps for each message
- **State Management**: Uses Zustand to manage conversation history
- **Performance**: Ready for virtualization if handling large conversation histories

### 🛠 Technical Stack

- **Framework**: Electron with React 19
- **Language**: TypeScript
- **State Management**: Zustand with persistence
- **Styling**: Custom CSS (Tailwind-ready architecture)
- **Markdown**: react-markdown with remark-gfm and rehype-highlight
- **Syntax Highlighting**: highlight.js with GitHub Dark theme
- **Build Tool**: Vite with Electron Forge

### 🎨 Design Philosophy

- **Clean & Minimal**: No placeholder content or welcome messages
- **Dark Theme**: Consistent dark color scheme throughout
- **VS Code-style**: Professional interface matching development tools
- **Accessibility First**: Proper keyboard navigation and focus management

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run start

# Build for production
npm run make
```

### Usage

1. **Starting a Conversation**: Type your message in the input area at the bottom
2. **Sending Messages**: Press Enter or click the Send button
3. **New Lines**: Use Shift+Enter for multiline messages
4. **History Navigation**: Use ↑/↓ arrow keys to navigate through previous prompts
5. **Markdown Support**: AI responses support full markdown formatting

### State Persistence

The application automatically saves:
- Conversation history
- Prompt history (last 100 prompts)
- User preferences

Data is stored locally using Zustand's persist middleware.

## Development

### Project Structure
```
src/
├── components/
│   ├── MainInterface.tsx    # Main conversation display
│   ├── PromptInput.tsx      # Bottom input component
│   └── index.ts            # Component exports
├── store.ts                # Zustand state management
├── App.tsx                 # Main application component
├── renderer.ts             # React app initialization
├── main.ts                 # Electron main process
├── preload.ts              # Electron preload script
└── index.css               # Global styles
```

### Key Features Implementation

- **Auto-resize Textarea**: Dynamic height adjustment based on content
- **History Navigation**: Cursor position-aware navigation
- **Markdown Rendering**: Custom component styling for dark theme
- **Message Types**: Distinct styling for user, AI, and tool messages
- **Persistence**: Automatic state saving and restoration
- **Performance**: Efficient re-rendering with Zustand selectors

## Customization

### Adding New Message Types
1. Update the `Message` interface in `store.ts`
2. Add styling in `MainInterface.tsx`
3. Update the message component logic

### Extending Markdown Support
- Modify the `ReactMarkdown` components in `MainInterface.tsx`
- Add new rehype/remark plugins as needed
- Update syntax highlighting themes in CSS

### Styling Modifications
- Update CSS custom properties in `index.css`
- Modify component-specific styles
- Add new utility classes as needed

## License

MIT License - see LICENSE file for details.
