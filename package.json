{"name": "arien agent", "productName": "arien agent", "version": "1.0.0", "description": "A minimal Electron desktop application", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": "ajay9", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.9.0", "@electron-forge/maker-deb": "^7.9.0", "@electron-forge/maker-rpm": "^7.9.0", "@electron-forge/maker-squirrel": "^7.9.0", "@electron-forge/maker-zip": "^7.9.0", "@electron-forge/plugin-auto-unpack-natives": "^7.9.0", "@electron-forge/plugin-fuses": "^7.9.0", "@electron-forge/plugin-vite": "^7.9.0", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^5.0.3", "electron": "38.1.2", "eslint": "^8.57.1", "eslint-plugin-import": "^2.32.0", "typescript": "~4.5.4", "vite": "^5.4.20"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "better-sqlite3": "^12.4.1", "electron-squirrel-startup": "^1.0.1", "highlight.js": "^11.11.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "zustand": "^5.0.8"}}