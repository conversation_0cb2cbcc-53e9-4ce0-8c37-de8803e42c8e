import React, { useCallback } from 'react';
import { MainInterface } from './components/MainInterface';
import { PromptInput } from './components/PromptInput';
import { useAppStore } from './store';

const App: React.FC = () => {
  const { addMessage } = useAppStore();

  const handlePromptSubmit = useCallback((prompt: string) => {
    // Add user message
    addMessage({
      type: 'user',
      content: prompt,
    });

    // Simulate AI response (replace with actual AI integration)
    setTimeout(() => {
      addMessage({
        type: 'ai',
        content: `I received your message: "${prompt}"\n\nThis is a **demo response** with *markdown* support:\n\n- Lists work\n- Code blocks work too\n\n\`\`\`javascript\nconst example = "Hello World";\nconsole.log(example);\n\`\`\`\n\n> Blockquotes are supported\n\nAnd [links](https://example.com) work as well!`,
      });
    }, 1000);
  }, [addMessage]);

  return (
    <div className="h-screen bg-primary text-primary flex flex-col overflow-hidden">
      <MainInterface />
      <PromptInput onSubmit={handlePromptSubmit} />
    </div>
  );
};

export default App;
