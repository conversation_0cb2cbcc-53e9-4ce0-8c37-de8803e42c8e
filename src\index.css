/* Custom CSS for Arien Agent */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background-color: #0a0a0a;
  color: #f0f0f0;
  line-height: 1.6;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
#app {
  height: 100vh;
  width: 100vw;
  background-color: #0a0a0a;
}



/* Scrollbar Styling for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* Modern Dark Theme Color Palette */
:root {
  --bg-primary: #0a0a0a;
  --bg-secondary: #111111;
  --bg-tertiary: #1a1a1a;
  --bg-surface: #1e1e1e;
  --bg-surface-hover: #252525;
  --bg-accent: #2563eb;
  --bg-accent-hover: #1d4ed8;
  --bg-user: rgba(37, 99, 235, 0.1);
  --bg-ai: rgba(16, 185, 129, 0.05);
  --bg-tool: rgba(245, 158, 11, 0.1);

  --text-primary: #f0f0f0;
  --text-secondary: #a1a1aa;
  --text-tertiary: #71717a;
  --text-accent: #60a5fa;
  --text-success: #10b981;
  --text-warning: #f59e0b;

  --border-primary: #27272a;
  --border-secondary: #3f3f46;
  --border-accent: rgba(37, 99, 235, 0.3);
  --border-user: rgba(37, 99, 235, 0.2);
  --border-ai: rgba(16, 185, 129, 0.2);
  --border-tool: rgba(245, 158, 11, 0.2);

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* Custom utility classes with modern design */
.h-screen { height: 100vh; }
.h-full { height: 100%; }
.w-full { width: 100%; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.top-0 { top: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.z-10 { z-index: 10; }

/* Modern Background Colors */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-surface { background-color: var(--bg-surface); }
.bg-surface-hover { background-color: var(--bg-surface-hover); }
.bg-accent { background-color: var(--bg-accent); }
.bg-user { background-color: var(--bg-user); }
.bg-ai { background-color: var(--bg-ai); }
.bg-tool { background-color: var(--bg-tool); }

/* Legacy color mappings for compatibility */
.bg-dark-bg { background-color: var(--bg-primary); }
.bg-dark-surface { background-color: var(--bg-surface); }
.bg-dark-accent { background-color: var(--bg-accent); }
.bg-dark-accent\/10 { background-color: var(--bg-user); }
/* Modern Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-accent { color: var(--text-accent); }
.text-success { color: var(--text-success); }
.text-warning { color: var(--text-warning); }
.text-white { color: #ffffff; }

/* Legacy text color mappings */
.text-dark-text { color: var(--text-primary); }
.text-dark-text-secondary { color: var(--text-secondary); }

/* Modern Border Colors */
.border-primary { border-color: var(--border-primary); }
.border-secondary { border-color: var(--border-secondary); }
.border-accent { border-color: var(--border-accent); }
.border-user { border-color: var(--border-user); }
.border-ai { border-color: var(--border-ai); }
.border-tool { border-color: var(--border-tool); }

/* Legacy border mappings */
.border-dark-border { border-color: var(--border-primary); }
.border-dark-accent\/30 { border-color: var(--border-accent); }

/* Border Utilities */
.border { border-width: 1px; }
.border-t { border-top-width: 1px; }
.border-l-2 { border-left-width: 2px; }
.border-l-4 { border-left-width: 4px; }

/* Border Radius */
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }

/* Spacing */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.pl-4 { padding-left: 1rem; }
.pr-12 { padding-right: 3rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-6 { padding-bottom: 1.5rem; }

.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.leading-relaxed { line-height: 1.625; }
.leading-tight { line-height: 1.25; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.italic { font-style: italic; }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transitions and Animations */
.transition-all { transition: all 0.2s ease-in-out; }
.transition-colors { transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out; }
.transition-transform { transition: transform 0.2s ease-in-out; }
.transition-opacity { transition: opacity 0.2s ease-in-out; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* Hover Effects */
.hover\:bg-surface-hover:hover { background-color: var(--bg-surface-hover); }
.hover\:bg-accent-hover:hover { background-color: var(--bg-accent-hover); }
.hover\:text-accent:hover { color: var(--text-accent); }
.hover\:border-accent:hover { border-color: var(--border-accent); }
.hover\:shadow-md:hover { box-shadow: var(--shadow-md); }
.hover\:scale-105:hover { transform: scale(1.05); }

/* Focus States */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--border-accent); }
.focus\:ring-accent:focus { box-shadow: 0 0 0 2px var(--border-accent); }
.focus\:border-accent:focus { border-color: var(--border-accent); }
.focus\:border-transparent:focus { border-color: transparent; }

/* Disabled States */
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* Sizing */
.max-w-none { max-width: none; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }

/* Gaps */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

/* Misc */
.resize-none { resize: none; }
.cursor-pointer { cursor: pointer; }
.select-none { user-select: none; }
.flex-shrink-0 { flex-shrink: 0; }
.inline-block { display: inline-block; }
.block { display: block; }
.hidden { display: none; }

/* Prose Styling for Markdown */
.prose { max-width: none; color: var(--text-primary); }
.prose-invert { color: var(--text-primary); }
.prose-sm { font-size: 0.875rem; line-height: 1.5; }

/* First/Last Child Utilities */
.first\:mt-0:first-child { margin-top: 0; }
.last\:mb-0:last-child { margin-bottom: 0; }

/* Backdrop Effects */
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur { backdrop-filter: blur(8px); }

/* Selection Styling */
::selection {
  background-color: #4a9eff;
  color: #ffffff;
}

::-moz-selection {
  background-color: var(--bg-accent);
  color: #ffffff;
}

/* Modern Component Styles */

/* Message Bubbles */
.message-bubble {
  border-radius: 1rem;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-primary);
  background: var(--bg-surface);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.message-bubble:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-secondary);
}

.message-bubble-user {
  background: var(--bg-user);
  border-color: var(--border-user);
  margin-left: 10%;
  margin-right: 2rem;
}

.message-bubble-ai {
  background: var(--bg-ai);
  border-color: var(--border-ai);
  margin-left: 2rem;
  margin-right: 10%;
}

.message-bubble-tool {
  background: var(--bg-tool);
  border-color: var(--border-tool);
  margin-left: 5%;
  margin-right: 5%;
}

/* Input Area */
.input-container {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  min-height: 140px;
  z-index: 10;
}

.input-textarea {
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: 0.75rem;
  transition: all 0.2s ease-in-out;
  resize: none;
  font-family: inherit;
}

.input-textarea:focus {
  outline: none;
  border-color: var(--border-accent);
  box-shadow: 0 0 0 2px var(--border-accent);
  background: var(--bg-surface-hover);
}

.input-textarea::placeholder {
  color: var(--text-tertiary);
}

.send-button {
  background: var(--bg-accent);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.send-button:hover:not(:disabled) {
  background: var(--bg-accent-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Scrollbar Enhancements */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
  transition: background 0.2s ease-in-out;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Code Block Enhancements */
.code-block {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 0.75rem 0;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.inline-code {
  background: var(--bg-tertiary);
  color: var(--text-accent);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875em;
  border: 1px solid var(--border-primary);
}

/* Table Enhancements */
.table-container {
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 0.5rem;
  border: 1px solid var(--border-primary);
  background: var(--bg-surface);
}

.table-container table {
  width: 100%;
  border-collapse: collapse;
}

.table-container th {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-weight: 600;
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.table-container td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.table-container tr:last-child td {
  border-bottom: none;
}

/* Blockquote Enhancements */
.blockquote {
  border-left: 4px solid var(--border-accent);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--text-secondary);
  background: var(--bg-surface);
  padding: 1rem;
  border-radius: 0 0.5rem 0.5rem 0;
}
