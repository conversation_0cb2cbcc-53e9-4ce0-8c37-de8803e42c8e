/* Custom CSS for Arien Agent */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, sans-serif;
  background-color: #1a1a1a;
  color: #e0e0e0;
  line-height: 1.6;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
#app {
  height: 100vh;
  width: 100vw;
  background-color: #1a1a1a;
}



/* Scrollbar Styling for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* Custom utility classes to replace Tailwind */
.h-screen { height: 100vh; }
.bg-dark-bg { background-color: #1a1a1a; }
.text-dark-text { color: #e0e0e0; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1; }
.overflow-y-auto { overflow-y: auto; }
.p-4 { padding: 1rem; }
.pb-32 { padding-bottom: 8rem; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.text-dark-text-secondary { color: #b0b0b0; }
.text-center { text-align: center; }
.text-lg { font-size: 1.125rem; }
.mb-2 { margin-bottom: 0.5rem; }
.text-sm { font-size: 0.875rem; }
.border { border-width: 1px; }
.rounded-lg { border-radius: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.bg-dark-accent\/10 { background-color: rgba(14, 165, 233, 0.1); }
.border-dark-accent\/30 { border-color: rgba(14, 165, 233, 0.3); }
.ml-8 { margin-left: 2rem; }
.bg-dark-surface { background-color: #2d2d2d; }
.border-dark-border { border-color: #404040; }
.mr-8 { margin-right: 2rem; }
.bg-yellow-900\/20 { background-color: rgba(113, 63, 18, 0.2); }
.border-yellow-600\/30 { border-color: rgba(202, 138, 4, 0.3); }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.justify-between { justify-content: space-between; }
.font-medium { font-weight: 500; }
.text-xs { font-size: 0.75rem; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.prose { max-width: none; }
.prose-invert { color: #e0e0e0; }
.prose-sm { font-size: 0.875rem; }
.max-w-none { max-width: none; }
.fixed { position: fixed; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.border-t { border-top-width: 1px; }
.gap-3 { gap: 0.75rem; }
.items-end { align-items: flex-end; }
.relative { position: relative; }
.w-full { width: 100%; }
.resize-none { resize: none; }
.bg-dark-bg { background-color: #1a1a1a; }
.text-dark-text { color: #e0e0e0; }
.placeholder-dark-text-secondary::placeholder { color: #b0b0b0; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.pr-12 { padding-right: 3rem; }
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.5); }
.focus\:ring-dark-accent:focus { --tw-ring-color: #0ea5e9; }
.focus\:border-transparent:focus { border-color: transparent; }
.transition-all { transition: all 0.2s; }
.duration-200 { transition-duration: 200ms; }
.absolute { position: absolute; }
.bottom-2 { bottom: 0.5rem; }
.right-2 { right: 0.5rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.rounded { border-radius: 0.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.bg-dark-accent { background-color: #0ea5e9; }
.text-white { color: #ffffff; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.flex-shrink-0 { flex-shrink: 0; }
.mt-2 { margin-top: 0.5rem; }
.inline-block { display: inline-block; }
.mr-4 { margin-right: 1rem; }
.h-full { height: 100%; }
.text-xl { font-size: 1.25rem; }
.font-bold { font-weight: 700; }
.mt-6 { margin-top: 1.5rem; }
.mt-5 { margin-top: 1.25rem; }
.mb-3 { margin-bottom: 0.75rem; }
.font-semibold { font-weight: 600; }
.first\:mt-0:first-child { margin-top: 0; }

/* Selection Styling */
::selection {
  background-color: #4a9eff;
  color: #ffffff;
}

::-moz-selection {
  background-color: #4a9eff;
  color: #ffffff;
}
